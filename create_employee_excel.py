#!/usr/bin/env python3
"""
Create Employee Excel Database for Multi-Source Testing
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import random

def create_employee_excel():
    """Create a comprehensive employee Excel database"""
    
    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    
    print("🏢 Creating Employee Excel Database...")
    
    # Sample employee data
    departments = ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations", "IT Support"]
    positions = {
        "Engineering": ["Software Engineer", "Senior Engineer", "Tech Lead", "Engineering Manager", "DevOps Engineer"],
        "Sales": ["Sales Representative", "Account Manager", "Sales Manager", "Business Development", "Sales Director"],
        "Marketing": ["Marketing Specialist", "Content Creator", "Marketing Manager", "Digital Marketing", "Brand Manager"],
        "HR": ["HR Specialist", "Recruiter", "HR Manager", "Training Coordinator", "HR Director"],
        "Finance": ["Accountant", "Financial Analyst", "Finance Manager", "Controller", "CFO"],
        "Operations": ["Operations Specialist", "Process Manager", "Operations Manager", "Supply Chain", "Operations Director"],
        "IT Support": ["Help Desk", "System Administrator", "IT Manager", "Network Engineer", "Security Specialist"]
    }
    
    locations = ["New York", "San Francisco", "Chicago", "Austin", "Seattle", "Boston", "Los Angeles"]
    
    # Generate employee data
    employees = []
    employee_id = 1001
    
    for dept in departments:
        dept_size = random.randint(8, 15)  # Each department has 8-15 employees
        
        for i in range(dept_size):
            # Generate realistic employee data
            first_names = ["John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "James", "Maria", 
                          "William", "Jennifer", "Richard", "Patricia", "Charles", "Linda", "Joseph", "Elizabeth"]
            last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", 
                         "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore"]
            
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            
            # Calculate hire date (between 6 months and 5 years ago)
            days_ago = random.randint(180, 1825)
            hire_date = datetime.now() - timedelta(days=days_ago)
            
            # Calculate salary based on department and position
            position = random.choice(positions[dept])
            base_salary = {
                "Engineering": random.randint(75000, 150000),
                "Sales": random.randint(50000, 120000),
                "Marketing": random.randint(55000, 100000),
                "HR": random.randint(50000, 95000),
                "Finance": random.randint(60000, 130000),
                "Operations": random.randint(55000, 110000),
                "IT Support": random.randint(50000, 90000)
            }
            
            salary = base_salary[dept]
            if "Manager" in position or "Director" in position or "Lead" in position:
                salary = int(salary * 1.3)  # 30% increase for management
            if "Senior" in position:
                salary = int(salary * 1.15)  # 15% increase for senior roles
            
            employee = {
                "EmployeeID": employee_id,
                "FirstName": first_name,
                "LastName": last_name,
                "FullName": f"{first_name} {last_name}",
                "Email": f"{first_name.lower()}.{last_name.lower()}@company.com",
                "Department": dept,
                "Position": position,
                "HireDate": hire_date.strftime("%Y-%m-%d"),
                "Salary": salary,
                "Location": random.choice(locations),
                "Status": random.choice(["Active", "Active", "Active", "Active", "On Leave"]),  # 80% active
                "Manager": f"Manager_{dept}",
                "Phone": f"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
                "YearsOfService": round((datetime.now() - hire_date).days / 365.25, 1)
            }
            
            employees.append(employee)
            employee_id += 1
    
    # Create DataFrame
    df = pd.DataFrame(employees)
    
    # Create Excel file with multiple sheets
    excel_path = "data/employees.xlsx"
    
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # Main employee sheet
        df.to_excel(writer, sheet_name='Employees', index=False)
        
        # Department summary sheet
        dept_summary = df.groupby('Department').agg({
            'EmployeeID': 'count',
            'Salary': ['mean', 'sum'],
            'YearsOfService': 'mean'
        }).round(2)
        dept_summary.columns = ['Employee_Count', 'Average_Salary', 'Total_Salary', 'Average_Years_Service']
        dept_summary.to_excel(writer, sheet_name='Department_Summary')
        
        # Location summary sheet
        location_summary = df.groupby('Location').agg({
            'EmployeeID': 'count',
            'Salary': 'mean'
        }).round(2)
        location_summary.columns = ['Employee_Count', 'Average_Salary']
        location_summary.to_excel(writer, sheet_name='Location_Summary')
        
        # Salary bands sheet
        df['SalaryBand'] = pd.cut(df['Salary'], 
                                 bins=[0, 60000, 80000, 100000, 120000, float('inf')],
                                 labels=['<60K', '60K-80K', '80K-100K', '100K-120K', '>120K'])
        salary_bands = df.groupby('SalaryBand').size().to_frame('Employee_Count')
        salary_bands.to_excel(writer, sheet_name='Salary_Bands')
    
    print(f"✅ Employee Excel database created: {excel_path}")
    print(f"📊 Total employees: {len(employees)}")
    print(f"🏢 Departments: {len(departments)}")
    print(f"📍 Locations: {len(locations)}")
    print(f"📋 Sheets created: Employees, Department_Summary, Location_Summary, Salary_Bands")
    
    # Display sample data
    print("\n📄 Sample Employee Data:")
    print(df.head(3).to_string())
    
    return excel_path

if __name__ == "__main__":
    create_employee_excel()
