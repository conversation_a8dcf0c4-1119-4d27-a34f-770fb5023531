"""
OpenAI LLM Client for SQL Generation
"""

import os
import asyncio
from typing import Optional
from loguru import logger

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI package not available. Install with: pip install openai")


class OpenAIClient:
    """OpenAI client for SQL generation"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-3.5-turbo"):
        """
        Initialize OpenAI client

        Args:
            api_key: OpenAI API key (if None, will use OPENAI_API_KEY env var)
            model: Model to use for generation
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not available. Install with: pip install openai")

        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model = model

        if not self.api_key:
            logger.warning("No OpenAI API key provided. Using mock responses.")
            self.mock_mode = True
            self.client = None
        else:
            # Initialize the new OpenAI client (v1.0+)
            self.client = openai.OpenAI(api_key=self.api_key)
            self.mock_mode = False
            logger.info(f"OpenAI client initialized with model: {model}")
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        Generate response using OpenAI

        Args:
            prompt: Input prompt
            **kwargs: Additional parameters

        Returns:
            Generated response
        """
        if self.mock_mode:
            return self._generate_mock_sql(prompt)

        try:
            # Use the new OpenAI client API (v1.0+)
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a SQL expert. Generate only valid SQL queries based on the provided schema and requirements."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1,  # Low temperature for consistent SQL generation
                **kwargs
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            # Fallback to mock response
            return self._generate_mock_sql(prompt)
    
    def _generate_mock_sql(self, prompt: str) -> str:
        """
        Generate mock SQL responses based on prompt analysis
        
        Args:
            prompt: Input prompt
            
        Returns:
            Mock SQL query
        """
        prompt_lower = prompt.lower()
        
        # Extract table names from schema
        tables = []
        if "employees" in prompt_lower:
            tables.append("Employees")
        if "einvoicing_taxcodelookup" in prompt_lower:
            tables.append("eInvoicing_TaxCodeLookup")
        if "taxcodes" in prompt_lower:
            tables.append("TaxCodes")
        
        # Extract query intent
        query_text = ""
        if "natural language query:" in prompt_lower:
            parts = prompt.split("Natural Language Query:")
            if len(parts) > 1:
                query_text = parts[1].split("\n")[0].strip()
        
        query_lower = query_text.lower()
        
        # Generate appropriate SQL based on analysis
        if tables:
            table = tables[0]
            sql = f"SELECT * FROM {table}"
            
            # Add WHERE clauses based on query analysis
            where_conditions = []
            
            # Name filtering
            if "first name" in query_lower and ("start" in query_lower or "begin" in query_lower):
                import re
                letter_match = re.search(r"(?:start|begin)s?\s+with\s+(?:letter\s+)?([A-Za-z])", query_text, re.IGNORECASE)
                if letter_match:
                    letter = letter_match.group(1).upper()
                    where_conditions.append(f"FirstName LIKE '{letter}%'")
            
            # Status filtering
            if "status" in query_lower:
                if "active" in query_lower:
                    where_conditions.append("Status = 'Active'")
                elif "inactive" in query_lower:
                    where_conditions.append("Status = 'Inactive'")
            elif "active" in query_lower and "employee" in query_lower:
                where_conditions.append("Status = 'Active'")
            
            # Years of service filtering
            if "year" in query_lower and "service" in query_lower:
                import re
                number_match = re.search(r"(?:more than|greater than|>)\s*(\d+)", query_text, re.IGNORECASE)
                if number_match:
                    years = number_match.group(1)
                    where_conditions.append(f"YearsOfService > {years}")
            
            # Department filtering
            if "department" in query_lower:
                if "hr" in query_lower:
                    where_conditions.append("Department = 'HR'")
                elif "engineering" in query_lower:
                    where_conditions.append("Department = 'Engineering'")
            
            # Salary filtering
            if "salary" in query_lower:
                import re
                number_match = re.search(r"(?:more than|greater than|>)\s*(\d+)", query_text, re.IGNORECASE)
                if number_match:
                    salary = number_match.group(1)
                    where_conditions.append(f"Salary > {salary}")
            
            # Add WHERE clause if conditions exist
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)
            
            # Add ORDER BY for tax codes
            if "tax" in query_lower and "code" in query_lower:
                sql += " ORDER BY TaxCode"
            elif not where_conditions:
                sql += f" ORDER BY {table}ID" if f"{table}ID" in prompt else ""
            
            logger.info(f"🤖 Mock SQL generated: {sql}")
            return sql
        
        # Default fallback
        return "SELECT * FROM Employees"


class SimpleLLMClient:
    """Simple LLM client that uses pattern matching for SQL generation"""
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate SQL using pattern matching"""
        return self._generate_pattern_sql(prompt)
    
    def _generate_pattern_sql(self, prompt: str) -> str:
        """Generate SQL using pattern matching"""
        client = OpenAIClient()  # Will use mock mode
        return client._generate_mock_sql(prompt)
