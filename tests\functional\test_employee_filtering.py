#!/usr/bin/env python3
"""
Test Employee Filtering Functionality
"""

import requests
import json

def test_employee_filtering():
    """Test employee filtering with different conditions"""
    
    print("🧪 Testing Employee Filtering Functionality")
    print("=" * 60)
    
    # Test queries with different filtering conditions
    test_queries = [
        {
            "query": "I need all employee for which year of service is greater than 3",
            "expected": "YearsOfService > 3"
        },
        {
            "query": "show me employees in HR department",
            "expected": "Department = 'HR'"
        },
        {
            "query": "get employees with salary greater than 80000",
            "expected": "Salary > 80000"
        },
        {
            "query": "list all employees in Engineering department",
            "expected": "Department = 'Engineering'"
        },
        {
            "query": "show employees with less than 2 years of service",
            "expected": "YearsOfService < 2"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n{i}️⃣ Testing: {test_case['query']}")
        print(f"   Expected condition: {test_case['expected']}")
        print("-" * 50)
        
        try:
            response = requests.post(
                "http://localhost:8007/query/excel-clean",
                json={"text": test_case['query']},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    sheet = data['sheets'][0]
                    metadata = data['metadata']
                    
                    print(f"   ✅ Query Success!")
                    print(f"   📊 Rows returned: {len(sheet.get('rows', []))}")
                    print(f"   🗄️ Datasource: {metadata.get('datasource')}")
                    print(f"   🔍 Generated SQL: {metadata.get('sql_query')}")
                    print(f"   🎯 Confidence: {metadata.get('confidence')}")
                    
                    # Show sample results
                    rows = sheet.get('rows', [])
                    if rows:
                        headers = sheet.get('headers', [])
                        print(f"   📄 Sample results:")
                        
                        # Find relevant columns
                        name_idx = headers.index('FullName') if 'FullName' in headers else None
                        dept_idx = headers.index('Department') if 'Department' in headers else None
                        years_idx = headers.index('YearsOfService') if 'YearsOfService' in headers else None
                        salary_idx = headers.index('Salary') if 'Salary' in headers else None
                        
                        for j, row in enumerate(rows[:3]):  # Show first 3 results
                            sample_info = []
                            if name_idx is not None:
                                sample_info.append(f"Name: {row[name_idx]}")
                            if dept_idx is not None:
                                sample_info.append(f"Dept: {row[dept_idx]}")
                            if years_idx is not None:
                                sample_info.append(f"Years: {row[years_idx]}")
                            if salary_idx is not None:
                                sample_info.append(f"Salary: {row[salary_idx]}")
                            
                            print(f"      {j+1}. {', '.join(sample_info)}")
                    
                    # Verify filtering worked
                    if len(rows) < 85:  # Total employees is 85
                        print(f"   🎯 Filtering appears to be working! ({len(rows)} < 85 total employees)")
                    else:
                        print(f"   ⚠️  No filtering applied - returned all {len(rows)} employees")
                        
                else:
                    print(f"   ❌ Query Failed: {data.get('error')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Employee Filtering Test Complete!")

if __name__ == "__main__":
    test_employee_filtering()
