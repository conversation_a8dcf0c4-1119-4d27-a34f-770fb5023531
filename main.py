#!/usr/bin/env python3
"""
Main entry point for Report Manager Orchestrator
"""

if __name__ == "__main__":
    import uvicorn

    print("🚀 Starting Report Manager Orchestrator")
    print("=" * 50)
    print("📊 Natural Language to Excel Reports")
    print("🌐 API Server: http://localhost:8000")
    print("📖 API Docs: http://localhost:8000/docs")
    print("=" * 50)

    # Start the FastAPI server
    uvicorn.run(
        "src.report_manager.api.server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
