
"""
FastAPI Server for Report Manager Orchestrator

This module provides a REST API interface for the orchestrator system.
"""

import asyncio
import os
import uuid
import json
import time
from typing import Optional, Dict, Any, List, AsyncGenerator
from datetime import datetime

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, Response
from pydantic import BaseModel
from dotenv import load_dotenv
from loguru import logger

from ..core.orchestrator import ReportManagerOrchestrator
from ..core.flow import FlowController
from ..utils.io import IOHandler
from ..core.models import OrchestrationResult
from ..database.query_engine import DatabaseQueryEngine
from ..database.security import DatabaseSecurity, UserSession
from ..llm.openai_client import OpenAIClient

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Report Manager Orchestrator API",
    description="AI-powered orchestrator that processes user text, creates context with LLM, and coordinates agent calls",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all incoming requests with detailed information"""
    start_time = time.time()

    # Get client info
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    # Log request start
    logger.info(f"🌐 INCOMING REQUEST")
    logger.info(f"   📍 {request.method} {request.url}")
    logger.info(f"   🖥️  Client: {client_ip}")
    logger.info(f"   🔧 User-Agent: {user_agent}")
    logger.info(f"   📋 Headers: {dict(request.headers)}")

    # Try to log request body for POST requests
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            body = await request.body()
            if body:
                # Try to parse as JSON for better formatting
                try:
                    json_body = json.loads(body.decode())
                    logger.info(f"   📦 Body (JSON): {json.dumps(json_body, indent=2)}")
                except:
                    logger.info(f"   📦 Body (Raw): {body.decode()[:500]}...")
            else:
                logger.info(f"   📦 Body: (empty)")
        except Exception as e:
            logger.warning(f"   ⚠️  Could not read request body: {e}")

    # Process the request
    try:
        response = await call_next(request)

        # Calculate processing time
        process_time = time.time() - start_time

        # Log response
        logger.info(f"✅ RESPONSE SENT")
        logger.info(f"   📊 Status: {response.status_code}")
        logger.info(f"   ⏱️  Processing time: {process_time:.3f}s")
        logger.info(f"   📋 Response headers: {dict(response.headers)}")

        # Add processing time to response headers
        response.headers["X-Process-Time"] = str(process_time)

        return response

    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"❌ REQUEST FAILED")
        logger.error(f"   📊 Error: {str(e)}")
        logger.error(f"   ⏱️  Processing time: {process_time:.3f}s")
        raise

# Global instances
orchestrator: Optional[ReportManagerOrchestrator] = None
flow_controller: Optional[FlowController] = None
db_query_engine: Optional[DatabaseQueryEngine] = None
io_handler = IOHandler()
active_flows: Dict[str, Dict[str, Any]] = {}


# Pydantic models for API
class QueryRequest(BaseModel):
    text: str
    complex_flow: bool = False
    output_format: str = "json"
    save_to_file: bool = False
    filename: Optional[str] = None


class QueryResponse(BaseModel):
    success: bool
    result: Any
    context: Dict[str, Any]
    agents_used: List[str]
    execution_time: float
    flow_id: Optional[str] = None
    error: Optional[str] = None


class FlowStatusResponse(BaseModel):
    flow_id: str
    total_steps: int
    completed_steps: int
    current_step: str
    steps: List[Dict[str, Any]]


class SystemStatusResponse(BaseModel):
    system: str
    status: str
    available_agents: List[str]
    agent_capabilities: Dict[str, Any]
    active_flows: int


class StreamQueryRequest(BaseModel):
    text: str
    complex_flow: bool = False
    output_format: str = "excel"
    include_metadata: bool = True
    chunk_size: int = 100


async def format_for_excel(result_data: Any, query: str) -> Dict[str, Any]:
    """
    Format the result data for Excel export
    """
    try:
        # Create Excel-friendly structure
        excel_data = {
            "metadata": {
                "title": f"Report: {query[:50]}...",
                "generated_at": datetime.now().isoformat(),
                "query": query
            },
            "sheets": []
        }

        # Handle QueryResult objects
        if hasattr(result_data, 'data'):
            actual_data = result_data.data
            query_type = getattr(result_data, 'query_type', 'unknown')

            # Add metadata from QueryResult
            excel_data["metadata"].update({
                "query_type": query_type,
                "success": getattr(result_data, 'success', True)
            })
        else:
            actual_data = result_data

        # Handle different types of data
        if isinstance(actual_data, dict):
            # Process each key-value pair as potential sheets
            for key, value in actual_data.items():
                if isinstance(value, list) and value:
                    if isinstance(value[0], dict):
                        # List of dictionaries - create a sheet
                        excel_data["sheets"].append({
                            "name": key.replace("_", " ").title(),
                            "headers": list(value[0].keys()),
                            "rows": [list(item.values()) if isinstance(item, dict) else [item] for item in value]
                        })
                    else:
                        # Simple list
                        excel_data["sheets"].append({
                            "name": key.replace("_", " ").title(),
                            "headers": ["Value"],
                            "rows": [[item] for item in value]
                        })
                elif isinstance(value, dict):
                    # Nested dictionary - convert to key-value pairs
                    excel_data["sheets"].append({
                        "name": key.replace("_", " ").title(),
                        "headers": ["Key", "Value"],
                        "rows": [[k, str(v)] for k, v in value.items()]
                    })
                else:
                    # Single value
                    if not any(sheet["name"] == "Summary" for sheet in excel_data["sheets"]):
                        excel_data["sheets"].append({
                            "name": "Summary",
                            "headers": ["Metric", "Value"],
                            "rows": []
                        })
                    # Find summary sheet and add row
                    for sheet in excel_data["sheets"]:
                        if sheet["name"] == "Summary":
                            sheet["rows"].append([key.replace("_", " ").title(), str(value)])
                            break

        elif isinstance(actual_data, list):
            if actual_data and isinstance(actual_data[0], dict):
                # List of dictionaries
                excel_data["sheets"].append({
                    "name": "Data",
                    "headers": list(actual_data[0].keys()),
                    "rows": [list(item.values()) if isinstance(item, dict) else [item] for item in actual_data]
                })
            else:
                # Simple list
                excel_data["sheets"].append({
                    "name": "Data",
                    "headers": ["Value"],
                    "rows": [[item] for item in actual_data]
                })

        else:
            # String or other data
            excel_data["sheets"].append({
                "name": "Results",
                "headers": ["Result"],
                "rows": [[str(actual_data)]]
            })

        # Ensure we have at least one sheet
        if not excel_data["sheets"]:
            excel_data["sheets"].append({
                "name": "No Data",
                "headers": ["Message"],
                "rows": [["No data available - configure database connection"]]
            })

        return excel_data

    except Exception as e:
        logger.error(f"Error formatting data for Excel: {e}")
        return {
            "metadata": {"title": "Error", "generated_at": datetime.now().isoformat()},
            "sheets": [{"name": "Error", "headers": ["Error"], "rows": [[str(e)]]}]
        }


def chunk_data(data: Dict[str, Any], chunk_size: int = 100) -> List[Dict[str, Any]]:
    """
    Split data into chunks for streaming
    """
    chunks = []

    for sheet in data.get("sheets", []):
        rows = sheet.get("rows", [])

        # Send metadata and headers first
        chunks.append({
            "sheet_name": sheet["name"],
            "headers": sheet["headers"],
            "type": "headers",
            "metadata": data.get("metadata", {})
        })

        # Split rows into chunks
        for i in range(0, len(rows), chunk_size):
            chunk_rows = rows[i:i + chunk_size]
            chunks.append({
                "sheet_name": sheet["name"],
                "rows": chunk_rows,
                "type": "rows",
                "chunk_index": i // chunk_size,
                "total_rows": len(rows)
            })

    return chunks


def initialize_system():
    """Initialize minimal system - no orchestrator required"""
    global orchestrator, flow_controller, db_query_engine

    # Disable orchestrator and flow controller for clean version
    orchestrator = None
    flow_controller = None

    # Try to initialize database query engine if database is available
    try:
        # Check if SQLite database is configured
        db_path = os.getenv("INVOICING_DB_PATH", "data/invoicing.db")

        # Debug logging
        logger.info(f"🔍 Database configuration check:")
        logger.info(f"   Type: SQLite")
        logger.info(f"   Path: {db_path}")
        logger.info(f"   Exists: {os.path.exists(db_path)}")

        if os.path.exists(db_path):
            logger.info("🔗 Database credentials found - initializing query engine")

            # Create OpenAI client for SQL generation
            llm_client = OpenAIClient()
            logger.info("🤖 OpenAI client initialized for SQL generation")

            # Initialize database query engine
            db_query_engine = DatabaseQueryEngine(llm_client)
            logger.info("✅ Database query engine created - will initialize on startup")
        else:
            db_query_engine = None
            logger.info("💡 SQLite database not found - database features disabled")
            logger.info(f"   Expected database at: {db_path}")
            logger.info("   Run 'python create_sqlite_db.py' to create the database")

    except Exception as e:
        logger.warning(f"⚠️ Failed to initialize database query engine: {e}")
        db_query_engine = None

    logger.info("✅ Clean system initialized - no mock data, no orchestrator dependencies")
    if db_query_engine:
        logger.info("🗄️ SQLite database query engine ready for real data")
    else:
        logger.info("💡 Create SQLite database for real data functionality")


@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    try:
        logger.info("🚀 STARTING REPORT MANAGER ORCHESTRATOR API")
        logger.info("=" * 60)
        initialize_system()

        # Initialize database connections if available
        global db_query_engine
        if db_query_engine:
            try:
                logger.info("🔧 Initializing database connections...")
                await db_query_engine.initialize()
                logger.info("✅ Database connections established successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize database connections: {e}")
                db_query_engine = None

        logger.info("✅ API server started successfully")
        logger.info("🌐 Available endpoints:")
        logger.info("   📍 GET  /health - Health check")
        logger.info("   📍 GET  /status - System status")
        logger.info("   📍 POST /query - Regular query")
        logger.info("   📍 POST /query/excel - Excel query")
        logger.info("   📍 POST /query/stream - Streaming query")
        logger.info("   📍 GET  /docs - API documentation")
        logger.info("=" * 60)
    except Exception as e:
        logger.error(f"❌ Failed to initialize system: {e}")
        raise


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Report Manager Orchestrator API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """
    Process a user query using the orchestrator
    """
    try:
        logger.info(f"🔍 REGULAR QUERY STARTED")
        logger.info(f"   📝 Query: {request.text}")
        logger.info(f"   ⚙️  Complex flow: {request.complex_flow}")
        logger.info(f"   📊 Output format: {request.output_format}")
        logger.info(f"   💾 Save to file: {request.save_to_file}")
        logger.info(f"   📁 Filename: {request.filename}")
        
        # Process the query
        if request.complex_flow:
            flow_id = str(uuid.uuid4())
            result = await flow_controller.execute_complex_flow(request.text, flow_id)
            
            # Store flow info for status tracking
            active_flows[flow_id] = {
                "created_at": datetime.now().isoformat(),
                "query": request.text,
                "status": "completed" if result.success else "failed"
            }
        else:
            result = await flow_controller.execute_simple_flow(request.text)
            flow_id = None
        
        # Prepare response
        response = QueryResponse(
            success=result.success,
            result=result.result,
            context={
                "intent": result.context.intent,
                "task_type": result.context.task_type.value,
                "entities": result.context.entities,
                "confidence": result.context.confidence,
                "parameters": result.context.parameters
            },
            agents_used=result.agents_used,
            execution_time=result.execution_time,
            flow_id=flow_id,
            error=result.error
        )
        
        # Save to file if requested
        if request.save_to_file and request.filename:
            background_tasks.add_task(
                save_result_to_file,
                response.dict(),
                request.filename,
                request.output_format
            )
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query/excel")
async def query_for_excel(request: QueryRequest):
    """🚀 ENTERPRISE INTELLIGENT QUERY ENDPOINT - Seamless Natural Language to Data"""
    try:
        print(f"🧠 ENTERPRISE QUERY: {request.text}")
        logger.info(f"🚀 INTELLIGENT ANALYSIS: {request.text}")

        # Use intelligent database query engine if available
        if db_query_engine:
            try:
                # Create a mock session for now (in enterprise, this would be real user session)
                class MockSession:
                    def __init__(self):
                        self.user_id = "system"
                        self.role = "admin"

                session = MockSession()

                # 🧠 LET AI INTELLIGENTLY DETERMINE:
                # 1. Which database to use (invoicing_db, sales_db, etc.)
                # 2. What SQL query to generate
                # 3. How to format the results
                # 4. What sheet names to use
                print(f"🔍 AI ANALYZING: '{request.text}'")
                result = await db_query_engine.execute_natural_language_query(request.text, session)

                if result.success:
                    print(f"✅ AI-GENERATED SQL: {result.sql_query}")
                    print(f"🗄️ AUTO-SELECTED DB: {result.datasource}")
                    logger.info(f"🧠 INTELLIGENT SQL: {result.sql_query}")
                    logger.info(f"📊 DATASOURCE: {result.datasource}")

                    # 🎯 INTELLIGENTLY DETERMINE SHEET NAME BASED ON CONTENT
                    sheet_name = "Data"
                    query_lower = request.text.lower()
                    if "tax" in query_lower and ("code" in query_lower or "einvoicing" in query_lower):
                        sheet_name = "Tax Codes"
                    elif "invoice" in query_lower:
                        sheet_name = "Invoices"
                    elif "customer" in query_lower:
                        sheet_name = "Customers"
                    elif "product" in query_lower:
                        sheet_name = "Products"
                    elif "sales" in query_lower:
                        sheet_name = "Sales Data"
                    elif "report" in query_lower:
                        sheet_name = "Report"

                    # 📊 BUILD INTELLIGENT RESPONSE
                    response_data = {
                        "success": True,
                        "metadata": {
                            "title": f"Report: {request.text}",
                            "generated_at": datetime.now().isoformat(),
                            "query": request.text,
                            "sql_query": result.sql_query,
                            "datasource": result.datasource,
                            "confidence": getattr(result, 'confidence', 1.0),
                            "explanation": "AI-powered intelligent query executed successfully",
                            "query_type": "enterprise_intelligent_analysis",
                            "ai_engine": "database_query_engine"
                        },
                        "sheets": [{
                            "name": sheet_name,
                            "headers": result.columns,
                            "rows": [list(row.values()) for row in result.data] if result.data else [],
                            "total_rows": len(result.data) if result.data else 0
                        }],
                        "summary": {
                            "total_records": len(result.data) if result.data else 0,
                            "datasets_returned": 1,
                            "ai_confidence": getattr(result, 'confidence', 1.0),
                            "processing_time": getattr(result, 'execution_time', 0),
                            "datasource_used": result.datasource,
                            "query_complexity": "intelligent_analysis"
                        }
                    }

                    print(f"📤 ENTERPRISE RESPONSE: {len(result.data) if result.data else 0} rows")
                    print(f"🎯 SHEET NAME: {sheet_name}")
                    logger.info(f"� INTELLIGENT RESPONSE: {len(result.data) if result.data else 0} rows from {result.datasource}")

                    return response_data

                else:
                    # Query failed - return detailed error
                    logger.error(f"AI query execution failed: {result.error}")
                    return {
                        "success": False,
                        "error": f"Query execution failed: {result.error}",
                        "metadata": {
                            "title": f"Report: {request.text}",
                            "generated_at": datetime.now().isoformat(),
                            "query": request.text,
                            "sql_query": getattr(result, 'sql_query', 'N/A'),
                            "datasource": getattr(result, 'datasource', 'N/A'),
                            "explanation": "AI query execution failed",
                            "query_type": "enterprise_intelligent_analysis"
                        }
                    }

            except Exception as e:
                logger.error(f"Enterprise query engine error: {e}")
                return {
                    "success": False,
                    "error": f"Enterprise query engine error: {str(e)}",
                    "metadata": {
                        "title": f"Report: {request.text}",
                        "generated_at": datetime.now().isoformat(),
                        "query": request.text,
                        "explanation": "Enterprise query engine encountered an error",
                        "query_type": "enterprise_intelligent_analysis"
                    }
                }

        # Database not available - enterprise setup required
        logger.warning("Enterprise database query engine not available")
        return {
            "success": False,
            "error": "Enterprise database query engine not available. Please check database configuration.",
            "metadata": {
                "title": f"Report: {request.text}",
                "generated_at": datetime.now().isoformat(),
                "query": request.text,
                "explanation": "Enterprise database query engine not initialized",
                "query_type": "enterprise_intelligent_analysis"
            },
            "setup_instructions": {
                "step1": "Ensure SQLite database exists at data/invoicing.db",
                "step2": "Run 'python create_sqlite_db.py' to create sample database",
                "step3": "Restart the server",
                "step4": "Queries will be processed intelligently by AI across all configured databases"
            }
        }

    except Exception as e:
        logger.error(f"Error in enterprise intelligent query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query/stream")
async def stream_query(request: StreamQueryRequest):
    """🚀 ENTERPRISE INTELLIGENT STREAMING ENDPOINT - Real-time AI-powered Data Streaming"""
    try:
        logger.info(f"🔄 ENTERPRISE STREAMING STARTED")
        logger.info(f"   🧠 AI Query: {request.text}")
        logger.info(f"   📊 Output format: {request.output_format}")
        logger.info(f"   📦 Chunk size: {request.chunk_size}")

        async def generate_stream() -> AsyncGenerator[str, None]:
            try:
                # Send initial metadata
                metadata = {
                    "type": "metadata",
                    "query": request.text,
                    "timestamp": datetime.now().isoformat(),
                    "format": request.output_format,
                    "ai_engine": "enterprise_intelligent_analysis"
                }
                yield f"data: {json.dumps(metadata)}\n\n"

                print(f"🧠 ENTERPRISE STREAM QUERY: {request.text}")
                logger.info(f"🚀 INTELLIGENT STREAM ANALYSIS: {request.text}")

                # Use intelligent database query engine
                if db_query_engine:
                    try:
                        # Create a mock session for now
                        class MockSession:
                            def __init__(self):
                                self.user_id = "system"
                                self.role = "admin"

                        session = MockSession()

                        # 🧠 LET AI INTELLIGENTLY DETERMINE EVERYTHING
                        print(f"🔍 AI STREAM ANALYZING: '{request.text}'")
                        result = await db_query_engine.execute_natural_language_query(request.text, session)

                        # DEBUG: Check result status
                        print(f"🔍 STREAM RESULT DEBUG:")
                        print(f"   Success: {result.success}")
                        print(f"   Error: {result.error}")
                        print(f"   SQL: {getattr(result, 'sql_query', 'N/A')}")
                        print(f"   Data count: {len(result.data) if hasattr(result, 'data') and result.data else 0}")

                        if result.success:
                            print(f"✅ AI STREAM SQL: {result.sql_query}")
                            print(f"🗄️ AI STREAM DB: {result.datasource}")
                            logger.info(f"🧠 STREAM INTELLIGENT SQL: {result.sql_query}")
                            logger.info(f"� STREAM DATASOURCE: {result.datasource}")

                            # 🎯 INTELLIGENTLY DETERMINE SHEET NAME
                            sheet_name = "Data"
                            query_lower = request.text.lower()
                            if "tax" in query_lower and ("code" in query_lower or "einvoicing" in query_lower):
                                sheet_name = "Tax Codes"
                            elif "invoice" in query_lower:
                                sheet_name = "Invoices"
                            elif "customer" in query_lower:
                                sheet_name = "Customers"
                            elif "product" in query_lower:
                                sheet_name = "Products"
                            elif "sales" in query_lower:
                                sheet_name = "Sales Data"
                            elif "report" in query_lower:
                                sheet_name = "Report"

                            # Format data for intelligent streaming
                            excel_data = {
                                "metadata": {
                                    "title": f"Report: {request.text}",
                                    "generated_at": datetime.now().isoformat(),
                                    "query": request.text,
                                    "sql_query": result.sql_query,
                                    "datasource": result.datasource,
                                    "confidence": getattr(result, 'confidence', 1.0),
                                    "explanation": "AI-powered intelligent streaming query executed successfully",
                                    "query_type": "enterprise_intelligent_streaming",
                                    "ai_engine": "database_query_engine"
                                },
                                "sheets": [{
                                    "name": sheet_name,
                                    "headers": result.columns,
                                    "rows": [list(row.values()) for row in result.data] if result.data else [],
                                    "total_rows": len(result.data) if result.data else 0
                                }]
                            }

                            print(f"📤 ENTERPRISE STREAM: {len(result.data) if result.data else 0} rows")
                            print(f"🎯 STREAM SHEET: {sheet_name}")
                            logger.info(f"� INTELLIGENT STREAM: {len(result.data) if result.data else 0} rows from {result.datasource}")

                            # Stream data in chunks
                            for chunk in chunk_data(excel_data, request.chunk_size):
                                chunk_payload = {
                                    "type": "data",
                                    "chunk": chunk,
                                    "timestamp": datetime.now().isoformat(),
                                    "ai_processed": True
                                }
                                yield f"data: {json.dumps(chunk_payload)}\n\n"

                            # Send completion signal
                            completion = {
                                "type": "complete",
                                "success": True,
                                "agents_used": ["enterprise_database_query_engine"],
                                "execution_time": getattr(result, 'execution_time', 0.1),
                                "ai_confidence": getattr(result, 'confidence', 1.0),
                                "datasource_used": result.datasource,
                                "error": None
                            }
                            yield f"data: {json.dumps(completion)}\n\n"
                            return

                    except Exception as e:
                        logger.error(f"Enterprise stream query failed: {e}")
                        error_payload = {
                            "type": "error",
                            "error": f"Enterprise query engine error: {str(e)}",
                            "timestamp": datetime.now().isoformat(),
                            "query_type": "enterprise_intelligent_streaming"
                        }
                        yield f"data: {json.dumps(error_payload)}\n\n"
                        return

                # Database not available
                error_payload = {
                    "type": "error",
                    "error": "Enterprise database query engine not available. Please check database configuration.",
                    "timestamp": datetime.now().isoformat(),
                    "query_type": "enterprise_intelligent_streaming",
                    "setup_instructions": {
                        "step1": "Ensure SQLite database exists at data/invoicing.db",
                        "step2": "Run 'python create_sqlite_db.py' to create sample database",
                        "step3": "Restart the server",
                        "step4": "Queries will be processed intelligently by AI across all configured databases"
                    }
                }
                yield f"data: {json.dumps(error_payload)}\n\n"

            except Exception as e:
                error_payload = {
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                    "query_type": "enterprise_intelligent_streaming"
                }
                yield f"data: {json.dumps(error_payload)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )

    except Exception as e:
        logger.error(f"Error in enterprise intelligent streaming: {e}")
        raise HTTPException(status_code=500, detail=str(e))





@app.get("/download/excel/{file_id}")
async def download_excel_file(file_id: str):
    """
    Download a generated Excel file
    """
    try:
        if not hasattr(app.state, 'excel_files') or file_id not in app.state.excel_files:
            raise HTTPException(status_code=404, detail="File not found")

        file_info = app.state.excel_files[file_id]

        # Clean up old files (older than 1 hour)
        if (datetime.now() - file_info['created_at']).total_seconds() > 3600:
            del app.state.excel_files[file_id]
            raise HTTPException(status_code=404, detail="File expired")

        return Response(
            content=file_info['data'],
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename="{file_info["filename"]}"'
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading Excel file: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """
    Get system status and available agents
    """
    try:
        logger.info(f"📊 SYSTEM STATUS requested")
        if orchestrator is None:
            logger.warning(f"⚠️  System not initialized!")
            raise HTTPException(status_code=503, detail="System not initialized")
        
        return SystemStatusResponse(
            system="Report Manager Orchestrator",
            status="running",
            available_agents=orchestrator.router_agent.list_available_agents(),
            agent_capabilities={
                agent: orchestrator.router_agent.get_agent_info(agent)
                for agent in orchestrator.router_agent.list_available_agents()
            },
            active_flows=len(active_flows)
        )
        
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/flows/{flow_id}", response_model=FlowStatusResponse)
async def get_flow_status(flow_id: str):
    """
    Get the status of a specific flow
    """
    try:
        if flow_controller is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        flow_status = flow_controller.get_flow_status(flow_id)
        
        if flow_status is None:
            raise HTTPException(status_code=404, detail="Flow not found")
        
        return FlowStatusResponse(**flow_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting flow status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/flows")
async def list_active_flows():
    """
    List all active flows
    """
    return {
        "active_flows": active_flows,
        "count": len(active_flows)
    }


@app.post("/test-connection")
async def test_llm_connection():
    """
    Test connection to LLM service
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        # Test with a simple query
        test_result = await orchestrator.create_context("Hello, test connection")
        
        return {
            "success": True,
            "test_query": "Hello, test connection",
            "response_received": True,
            "confidence": test_result.context.confidence,
            "intent": test_result.context.intent
        }
        
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@app.get("/agents")
async def list_agents():
    """
    List all available agents and their capabilities
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        agents = orchestrator.router_agent.list_available_agents()
        
        return {
            "agents": [
                {
                    "name": agent,
                    "info": orchestrator.router_agent.get_agent_info(agent)
                }
                for agent in agents
            ],
            "count": len(agents)
        }
        
    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/agents/{agent_name}")
async def get_agent_info(agent_name: str):
    """
    Get detailed information about a specific agent
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        agent_info = orchestrator.router_agent.get_agent_info(agent_name)
        
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return {
            "name": agent_name,
            "info": agent_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def save_result_to_file(result_data: Dict[str, Any], filename: str, format_type: str):
    """Background task to save result to file"""
    try:
        await io_handler.save_to_file(result_data, filename, format_type)
        logger.info(f"Result saved to {filename}")
    except Exception as e:
        logger.error(f"Failed to save result to {filename}: {e}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    logger.info(f"💚 HEALTH CHECK requested")
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "system_initialized": orchestrator is not None
    }


if __name__ == "__main__":
    import uvicorn
    
    # Configure logging
    logger.add("api_server.log", rotation="1 day", retention="7 days")
    
    # Run the server
    uvicorn.run(
        "api_server:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=os.getenv("API_RELOAD", "true").lower() == "true",
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )


