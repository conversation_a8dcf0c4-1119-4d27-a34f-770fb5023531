#!/usr/bin/env python3
"""
Debug regex pattern matching
"""

import re

def test_regex_patterns():
    query = "give me employees whose first name start with letter R"
    
    patterns = [
        r"starts with ([A-Za-z])",
        r"begins with ([A-Za-z])",
        r"starting with ([A-Za-z])",
        r"start with ([A-Za-z])",
        r"start with letter ([A-Za-z])",
        r"starts with letter ([A-Za-z])",
        r"begin with ([A-Za-z])",
        r"begin with letter ([A-Za-z])"
    ]
    
    print(f"Query: {query}")
    print("Testing patterns:")
    
    for pattern in patterns:
        match = re.search(pattern, query, re.IGNORECASE)
        if match:
            print(f"  ✅ Pattern '{pattern}' matched: '{match.group(1)}'")
        else:
            print(f"  ❌ Pattern '{pattern}' no match")

if __name__ == "__main__":
    test_regex_patterns()
