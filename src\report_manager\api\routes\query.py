"""
Query API Routes for Database Queries
"""

from fastapi import APIRouter, HTTPException, Depends, Head<PERSON>
from typing import Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
from ...database.query_engine import DatabaseQueryEngine
from ...database.security import UserSession
from loguru import logger

router = APIRouter()

# Global database query engine
db_query_engine: Optional[DatabaseQueryEngine] = None


class QueryRequest(BaseModel):
    """Request model for natural language queries"""
    text: str
    datasource: Optional[str] = None


class SQLRequest(BaseModel):
    """Request model for direct SQL queries"""
    sql: str
    datasource: str


class AuthRequest(BaseModel):
    """Request model for authentication"""
    username: str
    password: str


def get_session_token(authorization: str = Header(None)) -> str:
    """Extract session token from Authorization header"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")

    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization format")

    return authorization[7:]  # Remove "Bearer " prefix


async def get_user_session(token: str = Depends(get_session_token)) -> UserSession:
    """Get and validate user session"""
    if not db_query_engine:
        raise HTTPException(status_code=500, detail="Database engine not initialized")

    session = db_query_engine.security_manager.validate_session(token)
    if not session:
        raise HTTPException(status_code=401, detail="Invalid or expired session")

    return session


@router.post("/auth/login")
async def login_user(request: AuthRequest):
    """Authenticate user and return session token"""
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        session = db_query_engine.security_manager.authenticate_user(
            request.username,
            request.password
        )

        if session:
            return {
                "success": True,
                "session_token": session.session_token,
                "user_id": session.user_id,
                "role": session.role,
                "expires_at": session.expires_at.isoformat()
            }
        else:
            raise HTTPException(status_code=401, detail="Invalid credentials")

    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/query/excel")
async def execute_query_for_excel(
    request: QueryRequest,
    session: UserSession = Depends(get_user_session)
):
    """
    Main endpoint for frontend - converts natural language to Excel data

    User input determines database and table automatically.
    Examples:
    - "I need all einvoicing Tax code" → eInvoicing.TaxCodeLookup
    - "Show me customer data" → Customers table
    - "Get sales information" → Sales tables
    """
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        logger.info(f"Processing Excel query from {session.user_id}: {request.text}")

        # Execute the natural language query - system auto-determines DB and table
        result = await db_query_engine.execute_natural_language_query(
            request.text,
            session
        )

        if result.success:
            # Format response for Excel generation
            excel_data = {
                "success": True,
                "metadata": {
                    "title": f"Report: {request.text}",
                    "generated_at": datetime.now().isoformat(),
                    "user": session.user_id,
                    "query": request.text,
                    "sql_query": result.sql_query,
                    "datasource": result.datasource,
                    "execution_time": result.execution_time,
                    "confidence": result.metadata.get("sql_confidence", 0) if result.metadata else 0
                },
                "sheets": [{
                    "name": "Query Results",
                    "headers": result.columns or [],
                    "rows": [list(row.values()) for row in result.data] if result.data else [],
                    "total_rows": result.row_count
                }],
                "raw_data": result.data,  # For frontend processing if needed
                "summary": {
                    "total_records": result.row_count,
                    "columns_count": len(result.columns) if result.columns else 0,
                    "datasource_used": result.datasource,
                    "tables_accessed": result.tables or []
                }
            }

            return excel_data
        else:
            return {
                "success": False,
                "error": result.error,
                "metadata": {
                    "title": f"Failed Query: {request.text}",
                    "generated_at": datetime.now().isoformat(),
                    "user": session.user_id,
                    "query": request.text,
                    "sql_query": result.sql_query,
                    "datasource": result.datasource
                }
            }

    except Exception as e:
        logger.error(f"Excel query execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/query/direct")
async def execute_direct_sql(
    request: SQLRequest,
    session: UserSession = Depends(get_user_session)
):
    """Execute direct SQL query"""
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        logger.info(f"Executing direct SQL from {session.user_id}: {request.sql}")

        result = await db_query_engine.execute_sql_query(
            request.sql,
            request.datasource,
            session
        )

        if result.success:
            return {
                "success": True,
                "data": result.data,
                "columns": result.columns,
                "row_count": result.row_count,
                "execution_time": result.execution_time
            }
        else:
            return {
                "success": False,
                "error": result.error
            }

    except Exception as e:
        logger.error(f"Direct SQL execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/tables")
async def get_available_tables(session: UserSession = Depends(get_user_session)):
    """Get available tables for the user"""
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        tables = await db_query_engine.get_available_tables(session)
        return tables

    except Exception as e:
        logger.error(f"Error getting tables: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/schema/{datasource}/{table}")
async def get_table_schema(
    datasource: str,
    table: str,
    session: UserSession = Depends(get_user_session)
):
    """Get schema for a specific table"""
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        schema = await db_query_engine.get_table_schema(datasource, table, session)

        if schema:
            return schema
        else:
            raise HTTPException(status_code=404, detail="Table not found or access denied")

    except Exception as e:
        logger.error(f"Error getting table schema: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/query/suggestions")
async def get_query_suggestions(
    q: str,
    session: UserSession = Depends(get_user_session)
):
    """Get query suggestions based on partial input"""
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        suggestions = await db_query_engine.suggest_queries(q, session)
        return {"suggestions": suggestions}

    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def initialize_query_routes(query_engine: DatabaseQueryEngine):
    """Initialize the query routes with database engine"""
    global db_query_engine
    db_query_engine = query_engine
    logger.info("Query routes initialized with database engine")