#!/usr/bin/env python3
"""
Test Specific Name Query That's Failing
"""

import requests
import json

def test_specific_name_query():
    """Test the specific query that's failing"""
    
    print("🧪 Testing Specific Name Query")
    print("=" * 60)
    
    # Test the exact query that's failing
    query = "give me employees whose first name start with letter R"
    
    print(f"🔍 Testing Query: {query}")
    print("-" * 60)
    
    try:
        response = requests.post(
            "http://localhost:8009/query/excel-clean",
            json={"text": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                sheet = data['sheets'][0]
                metadata = data['metadata']
                
                print(f"✅ Query Success!")
                print(f"📊 Rows returned: {len(sheet.get('rows', []))}")
                print(f"🗄️ Datasource: {metadata.get('datasource')}")
                print(f"🔍 Generated SQL: {metadata.get('sql_query')}")
                print(f"🎯 Confidence: {metadata.get('confidence')}")
                
                # Show sample results
                rows = sheet.get('rows', [])
                headers = sheet.get('headers', [])
                
                if rows:
                    print(f"📄 Sample results:")
                    
                    # Find name column
                    name_idx = None
                    for idx, header in enumerate(headers):
                        if 'name' in header.lower() or 'fullname' in header.lower():
                            name_idx = idx
                            break
                    
                    # Show first 10 results
                    for j, row in enumerate(rows[:10]):
                        if name_idx is not None:
                            name = row[name_idx]
                            first_name = name.split()[0] if name else "Unknown"
                            print(f"   {j+1}. {name} (First: {first_name})")
                        else:
                            print(f"   {j+1}. {row[:3]}...")  # Show first 3 columns
                    
                    # Verify filtering
                    if name_idx is not None:
                        names_with_r = []
                        names_without_r = []
                        
                        for row in rows:
                            if row[name_idx]:
                                full_name = str(row[name_idx])
                                first_name = full_name.split()[0] if full_name else ""
                                if first_name.startswith('R'):
                                    names_with_r.append(full_name)
                                else:
                                    names_without_r.append(full_name)
                        
                        print(f"\n🎯 Filtering Verification:")
                        print(f"   Total rows: {len(rows)}")
                        print(f"   Names starting with R: {len(names_with_r)}")
                        print(f"   Names NOT starting with R: {len(names_without_r)}")
                        
                        if len(names_without_r) == 0 and len(names_with_r) > 0:
                            print(f"   ✅ PERFECT! All names start with R")
                        elif len(names_without_r) > 0:
                            print(f"   ❌ ISSUE: {len(names_without_r)} names don't start with R")
                            # Show examples of non-R names
                            for k, name in enumerate(names_without_r[:5]):
                                first_name = name.split()[0] if name else "Unknown"
                                print(f"      Example: {name} (First: {first_name})")
                        else:
                            print(f"   ⚠️  No names found starting with R")
                    
                    if len(rows) < 85:  # Total employees
                        print(f"   ✅ FILTERING APPLIED! ({len(rows)} < 85 total employees)")
                    else:
                        print(f"   ❌ NO FILTERING: Returned all {len(rows)} employees")
                
            else:
                print(f"❌ Query Failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Specific Name Query Test Complete!")

if __name__ == "__main__":
    test_specific_name_query()
