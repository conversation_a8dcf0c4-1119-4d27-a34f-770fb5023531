# 🚀 Quick Start Guide - Enterprise Multi-Database Query System

## 🎯 What This System Does

Transform natural language into database queries across multiple databases automatically.

**Example**: 
- Input: "Show me employees with more than 3 years of service"
- Output: Excel file with 37 filtered employees (not all 85)

---

## ⚡ Quick Start (5 minutes)

### 1. Start the Server
```bash
cd report-manager-orchestrator
python enterprise_server.py
```

### 2. Test the System
```bash
# Health check
curl http://localhost:8009/health

# Employee query (Excel database)
curl -X POST http://localhost:8009/query/excel-clean \
  -H "Content-Type: application/json" \
  -d '{"text": "Show me all employees"}'

# Tax code query (SQLite database)
curl -X POST http://localhost:8009/query/excel-clean \
  -H "Content-Type: application/json" \
  -d '{"text": "Get all tax codes from einvoicing"}'
```

---

## 🗄️ Current Databases

| Database | Type | Example Query |
|----------|------|---------------|
| **employee_db** | Excel | "Show HR employees with salary > 80000" |
| **invoicing_db** | SQLite | "Get all GST tax codes" |

---

## 📝 Example Queries

### Employee Queries → Excel Database
```
✅ "Show me all employees"
✅ "Find employees with more than 3 years of service"
✅ "List HR department employees"
✅ "Get employees with salary over 80000"
✅ "Show engineering department staff"
```

### Tax Queries → SQLite Database
```
✅ "Get all tax codes from einvoicing"
✅ "Show me GST tax rates"
✅ "List all tax codes"
✅ "Find CGST tax codes"
```

---

## 🔧 Adding a New Database (2 minutes)

### Step 1: Edit Configuration
Add to `config/database_config.yaml`:

```yaml
data_sources:
  your_new_db:
    name: "Your Database Name"
    type: "postgresql"  # or mysql, sqlite, excel
    connection:
      host: "your-server.com"
      database: "your_db"
      username: "${DB_USER}"
      password: "${DB_PASSWORD}"
    keywords: ["your", "keywords", "here"]

routing_rules:
  intents:
    your_queries:
      keywords: ["your", "keywords"]
      primary_datasource: "your_new_db"
      priority: 1
```

### Step 2: Set Environment Variables
```bash
# .env file
DB_USER=your_username
DB_PASSWORD=your_password
```

### Step 3: Restart Server
```bash
python enterprise_server.py
```

### Step 4: Test
```bash
curl -X POST http://localhost:8009/query/excel-clean \
  -H "Content-Type: application/json" \
  -d '{"text": "your query here"}'
```

---

## 🌐 API Endpoints

### Main Query Endpoint
```http
POST /query/excel-clean
Content-Type: application/json

{
  "text": "Your natural language query here"
}
```

### Health Check
```http
GET /health
```

### Response Format
```json
{
  "success": true,
  "sheets": [{
    "name": "Sheet Name",
    "headers": ["Column1", "Column2"],
    "rows": [["Value1", "Value2"]]
  }],
  "metadata": {
    "sql_query": "Generated SQL",
    "datasource": "Database used",
    "confidence": 1.0
  }
}
```

---

## 🔍 Troubleshooting

### Server Won't Start
```bash
# Check if port is in use
netstat -an | findstr :8009

# Try different port
# Edit enterprise_server.py, change port to 8010
```

### Query Returns No Results
1. Check if keywords match database configuration
2. Verify database file exists
3. Test with health endpoint first

### "No connection available"
1. Check database file paths in config
2. Verify environment variables
3. Check database permissions

---

## 📁 Key Files

```
├── enterprise_server.py              # Main server (START HERE)
├── config/database_config.yaml       # All configuration
├── data/
│   ├── invoicing.db                  # SQLite database
│   └── employees.xlsx                # Excel database
├── ENTERPRISE_ARCHITECTURE.md        # Full documentation
└── QUICK_START_GUIDE.md             # This file
```

---

## 🎯 Key Features

- **🧠 AI-Powered**: Natural language to SQL conversion
- **🔄 Multi-Database**: SQLite, Excel, PostgreSQL, MySQL support
- **⚙️ Configuration-Driven**: No hardcoding, all behavior configurable
- **🚀 Plug-and-Play**: Add databases without code changes
- **📊 Excel-Ready**: Returns data in Excel-compatible format

---

## 💡 Pro Tips

1. **Use specific keywords**: "employees" routes to Excel, "tax codes" routes to SQLite
2. **Be descriptive**: "employees with salary > 80000" works better than "high paid staff"
3. **Check health first**: Always verify `/health` endpoint before querying
4. **Read the logs**: Server console shows detailed query processing
5. **Test incrementally**: Start with simple queries, then add complexity

---

## 🆘 Need Help?

1. **Read**: `ENTERPRISE_ARCHITECTURE.md` for complete documentation
2. **Check**: Server logs for detailed error messages
3. **Verify**: Database connections with health endpoint
4. **Test**: Simple queries first, then complex ones

**Remember**: This system is designed to be completely dynamic. You should never need to modify code - only configuration!
