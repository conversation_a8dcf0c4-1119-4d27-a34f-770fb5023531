# Enterprise Database Configuration for Invoicing System
global_settings:
  query_timeout: 30
  max_connections_per_pool: 10
  enable_auto_discovery: true
  cache_schema_info: true
  schema_cache_ttl: 3600

security:
  enable_sql_injection_protection: true
  enable_query_validation: true
  max_query_length: 10000
  allowed_operations: ["SELECT"]
  query_result_limit: 10000

data_sources:
  invoicing_db:
    name: "Invoicing Database (SQLite)"
    description: "Local SQLite database with tax code data"
    type: "sqlite"
    connection:
      database: "${INVOICING_DB_PATH:data/invoicing.db}"

    auto_discovery:
      enabled: true
      include_tables: ["eInvoicing_TaxCodeLookup"]
      exclude_tables: ["sqlite_sequence", "sqlite_master"]
      include_views: false
      cache_schema: true

    business_context:
      domain: "invoicing"
      keywords: ["tax", "invoice", "billing", "codes", "rates", "gst", "vat", "einvoicing"]

  employee_db:
    name: "Employee Database (Excel)"
    description: "Excel database with employee information"
    type: "excel"
    connection:
      file_path: "${EMPLOYEE_DB_PATH:data/employees.xlsx}"
      sheet_name: "Employees"  # Primary sheet for queries

    auto_discovery:
      enabled: true
      include_sheets: ["Employees", "Department_Summary", "Location_Summary", "Salary_Bands"]
      cache_schema: true

    business_context:
      domain: "hr"
      keywords: ["employee", "staff", "hr", "department", "salary", "location", "hire", "position", "manager"]

routing_rules:
  intents:
    # HIGH PRIORITY - Exact eInvoicing tax code queries
    einvoicing_tax_codes:
      keywords: ["einvoicing tax code", "einvoicing tax", "tax code lookup"]
      primary_datasource: "invoicing_db"
      priority: 1
      exact_match: true

    # HIGH PRIORITY - Employee queries
    employee_queries:
      keywords: ["employee", "staff", "hr", "department", "salary", "hire", "position", "manager", "location"]
      primary_datasource: "employee_db"
      priority: 1
      exact_match: false

    # MEDIUM PRIORITY - General tax queries
    tax_queries:
      keywords: ["tax", "codes", "rates", "gst", "vat", "duties"]
      primary_datasource: "invoicing_db"
      priority: 2

    # LOW PRIORITY - Other queries
    invoice_queries:
      keywords: ["invoice", "billing", "payments", "customers"]
      primary_datasource: "invoicing_db"
      priority: 3

  fallback:
    default_datasource: "invoicing_db"

access_control:
  roles:
    admin:
      description: "Full access to all databases"
      permissions: ["invoicing_db.*", "employee_db.*"]

    hr_manager:
      description: "Full access to employee data"
      permissions: ["employee_db.*"]

    hr_user:
      description: "Basic employee data access"
      permissions: ["employee_db.Employees", "employee_db.Department_Summary"]

    tax_user:
      description: "Tax codes and rates access"
      permissions: ["invoicing_db.TaxCodes", "invoicing_db.eInvoicing.*"]

    readonly:
      description: "Read-only access to basic data"
      permissions: ["invoicing_db.TaxCodes", "employee_db.Employees"]