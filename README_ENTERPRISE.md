# 🏗️ Enterprise Multi-Database Query System

A **truly dynamic, AI-powered, multi-database query orchestrator** that converts natural language into SQL queries and routes them to appropriate databases automatically.

## 🎯 What It Does

Transform natural language into database queries across multiple databases:

```
Input:  "Show me employees with more than 3 years of service"
Output: Excel file with 37 filtered employees (not all 85)

Input:  "Get all tax codes from einvoicing"  
Output: SQLite query returning 32 tax codes
```

## ✨ Key Features

- ✅ **Zero Hardcoding**: All behavior is configuration-driven
- ✅ **Natural Language Processing**: Converts human text to SQL
- ✅ **Multi-Database Support**: SQLite, Excel, PostgreSQL, MySQL, etc.
- ✅ **Intelligent Routing**: AI determines which database to query
- ✅ **Dynamic Schema Discovery**: Automatically learns database structures
- ✅ **Plug-and-Play**: Add new databases without code changes

## 🚀 Quick Start (30 seconds)

```bash
# 1. Start the server
python enterprise_server.py

# 2. Test employee query (Excel database)
curl -X POST http://localhost:8009/query/excel-clean \
  -H "Content-Type: application/json" \
  -d '{"text": "Show me all employees"}'

# 3. Test tax query (SQLite database)  
curl -X POST http://localhost:8009/query/excel-clean \
  -H "Content-Type: application/json" \
  -d '{"text": "Get all tax codes"}'
```

## 🗄️ Current Databases

| Database | Type | File | Example Query |
|----------|------|------|---------------|
| **employee_db** | Excel | `data/employees.xlsx` | "Show HR employees with salary > 80000" |
| **invoicing_db** | SQLite | `data/invoicing.db` | "Get all GST tax codes from einvoicing" |

## 📚 Documentation

- **[📖 ENTERPRISE_ARCHITECTURE.md](ENTERPRISE_ARCHITECTURE.md)** - Complete system documentation
- **[🚀 QUICK_START_GUIDE.md](QUICK_START_GUIDE.md)** - Get started in 5 minutes
- **[⚙️ config/database_config.yaml](config/database_config.yaml)** - All system configuration

## 🔧 Adding New Databases (2 minutes)

Just edit `config/database_config.yaml`:

```yaml
data_sources:
  your_new_db:
    type: "postgresql"  # or mysql, sqlite, excel
    connection:
      host: "your-server.com"
      database: "your_db"
    keywords: ["your", "keywords", "here"]
```

No code changes needed! 🎉

## 🌐 API

### Main Endpoint
```http
POST /query/excel-clean
Content-Type: application/json

{
  "text": "Your natural language query here"
}
```

### Health Check
```http
GET /health
```

## 🎯 Example Queries

### Employee Queries → Excel Database
- "Show me all employees"
- "Find employees with more than 3 years of service"  
- "List HR department employees"
- "Get employees with salary over 80000"

### Tax Queries → SQLite Database
- "Get all tax codes from einvoicing"
- "Show me GST tax rates"
- "List all CGST tax codes"

## 🏛️ Architecture

```
User Query → Intent Analysis → Database Routing → SQL Generation → Query Execution → Results
```

**Key Components:**
- **Query Engine**: Main orchestrator
- **SQL Agent**: AI-powered SQL generation  
- **Connection Manager**: Multi-database support
- **Schema Discovery**: Automatic structure detection
- **Configuration Manager**: Zero-hardcoding system

## 📁 Project Structure

```
├── enterprise_server.py              # Main server (START HERE)
├── config/database_config.yaml       # All configuration  
├── data/                             # Database files
├── src/report_manager/               # Core system
├── ENTERPRISE_ARCHITECTURE.md        # Full documentation
└── QUICK_START_GUIDE.md             # Quick start guide
```

## 🔍 Troubleshooting

1. **Check health**: `curl http://localhost:8009/health`
2. **Read logs**: Server console shows detailed processing
3. **Verify config**: Check `config/database_config.yaml`
4. **Test simple**: Start with basic queries

## 🎉 Success Stories

- ✅ **37 employees** filtered from 85 total (YearsOfService > 3)
- ✅ **14 HR employees** filtered from 85 total  
- ✅ **32 tax codes** from SQLite einvoicing table
- ✅ **Zero hardcoding** - all behavior configuration-driven

**This system proves that enterprise-grade, AI-powered, multi-database query orchestration can be achieved with zero hardcoding!** 🚀

## 🆘 Need Help?

1. **Quick Start**: Read `QUICK_START_GUIDE.md`
2. **Full Docs**: Read `ENTERPRISE_ARCHITECTURE.md`
3. **Configuration**: Check `config/database_config.yaml`
4. **Health Check**: Test `/health` endpoint first

## 🔑 Key Principles

- **Configuration Over Code**: All behavior defined in YAML
- **AI-First Design**: Natural language understanding built-in
- **Plugin Architecture**: Easy database addition
- **Schema Agnostic**: Automatic discovery
- **Enterprise Ready**: Scalable and secure
