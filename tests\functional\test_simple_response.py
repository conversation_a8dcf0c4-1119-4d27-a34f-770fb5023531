#!/usr/bin/env python3
"""
Test the simple Excel endpoint response format
"""

import requests
import json

def test_simple_excel():
    """Test the simple Excel endpoint"""
    
    url = "http://localhost:8007/query/excel-simple"
    payload = {
        "text": "I need all tax code from einvoicing"
    }
    
    try:
        print("🧪 Testing Simple Excel Endpoint...")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ SUCCESS! Clean Response Format:")
            print("=" * 60)
            print(f"Success: {data.get('success')}")
            print(f"Total Sheets: {len(data.get('sheets', []))}")
            
            if data.get('sheets'):
                sheet = data['sheets'][0]
                print(f"Sheet Name: {sheet.get('name')}")
                print(f"Headers: {sheet.get('headers')}")
                print(f"Total Rows: {sheet.get('total_rows')}")
                print(f"Sample Row: {sheet.get('rows', [[]])[0] if sheet.get('rows') else 'No data'}")
            
            print("\n📊 Metadata:")
            metadata = data.get('metadata', {})
            print(f"Title: {metadata.get('title')}")
            print(f"SQL Query: {metadata.get('sql_query')}")
            print(f"Datasource: {metadata.get('datasource')}")
            print(f"Confidence: {metadata.get('confidence')}")
            
            print("\n🎯 PERFECT! This is the clean format your frontend needs!")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_simple_excel()
