#!/usr/bin/env python3
import requests

def test_ai_sql():
    queries = [
        "give me employees whose first name start with letter R",
        "All employee with status Active",
        "employees with more than 3 years of service",
        "Get all tax codes from einvoicing"
    ]
    
    for query in queries:
        print(f"\n🧪 Testing: {query}")
        print("-" * 50)
        
        response = requests.post(
            "http://localhost:8010/query/excel-clean",
            json={"text": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                sheet = data['sheets'][0]
                metadata = data['metadata']
                
                print(f"✅ Success: {len(sheet.get('rows', []))} rows")
                print(f"🤖 AI Generated SQL: {metadata.get('sql_query')}")
                print(f"🗄️ Datasource: {metadata.get('datasource')}")
                print(f"🎯 Explanation: {metadata.get('explanation', 'N/A')}")
            else:
                print(f"❌ Failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")

if __name__ == "__main__":
    test_ai_sql()
