#!/usr/bin/env python3
"""
Test the ultra-clean Excel endpoint response format
"""

import requests
import json

def test_clean_excel():
    """Test the ultra-clean Excel endpoint"""
    
    url = "http://localhost:8007/query/excel-clean"
    payload = {
        "text": "I need all tax code from einvoicing"
    }
    
    try:
        print("🎯 Testing Ultra-Clean Excel Endpoint...")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ ULTRA-CLEAN Response Format:")
            print("=" * 60)
            print(f"Success: {data.get('success')}")
            print(f"Keys in response: {list(data.keys())}")
            
            if data.get('sheets'):
                sheet = data['sheets'][0]
                print(f"Sheet Name: {sheet.get('name')}")
                print(f"Headers Count: {len(sheet.get('headers', []))}")
                print(f"Rows Count: {len(sheet.get('rows', []))}")
                print(f"First Header: {sheet.get('headers', ['N/A'])[0]}")
                print(f"Sample Row: {sheet.get('rows', [[]])[0][:3] if sheet.get('rows') else 'No data'}...")
            
            print("\n📊 Metadata:")
            metadata = data.get('metadata', {})
            print(f"Title: {metadata.get('title')}")
            print(f"SQL Query: {metadata.get('sql_query')}")
            print(f"Datasource: {metadata.get('datasource')}")
            
            print("\n🎯 PERFECT! No duplicates, no nesting, clean structure!")
            print("\n📋 Full Response Structure:")
            print(json.dumps(data, indent=2)[:500] + "..." if len(json.dumps(data)) > 500 else json.dumps(data, indent=2))
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_clean_excel()
