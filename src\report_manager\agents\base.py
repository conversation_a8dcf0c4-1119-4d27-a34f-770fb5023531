"""
Base Agent Classes for Report Manager Orchestrator

This module provides base classes and interfaces for all agents.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from ..core.models import UserContext


class BaseAgent(ABC):
    """Abstract base class for all agents"""

    def __init__(self, name: str, capabilities: Optional[List[str]] = None):
        """
        Initialize the base agent

        Args:
            name: Name of the agent
            capabilities: List of capabilities this agent supports
        """
        self.name = name
        self.capabilities: List[str] = capabilities or []
        self.priority: int = 1

    @abstractmethod
    async def process(self, context: UserContext) -> Any:
        """
        Process a request based on user context

        Args:
            context: User context from orchestrator

        Returns:
            Processing result
        """
        pass

    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return self.capabilities

    def get_priority(self) -> int:
        """Get agent priority"""
        return self.priority

    def can_handle(self, context: UserContext) -> bool:
        """
        Check if this agent can handle the given context

        Args:
            context: User context

        Returns:
            True if agent can handle the context
        """
        return True  # Default implementation


class AgentRegistry:
    """Registry for managing available agents"""

    def __init__(self):
        self._agents: Dict[str, BaseAgent] = {}

    def register(self, agent: BaseAgent):
        """Register an agent"""
        self._agents[agent.name] = agent

    def get_agent(self, name: str) -> Optional[BaseAgent]:
        """Get an agent by name"""
        return self._agents.get(name)

    def list_agents(self) -> List[str]:
        """List all registered agent names"""
        return list(self._agents.keys())

    def get_agents_for_context(self, context: UserContext) -> List[BaseAgent]:
        """Get agents that can handle the given context"""
        return [agent for agent in self._agents.values() if agent.can_handle(context)]


# Global agent registry
agent_registry = AgentRegistry()