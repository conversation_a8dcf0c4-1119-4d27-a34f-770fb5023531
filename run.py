#!/usr/bin/env python3
"""
Alternative startup script for Report Manager Orchestrator
"""

import subprocess
import sys
import os

def run_server():
    """Run the server using uvicorn directly"""
    try:
        print("🚀 Starting Report Manager Orchestrator")
        print("🌐 Server will be available at: http://localhost:8000")
        print("📖 API Documentation at: http://localhost:8000/docs")
        print("=" * 60)
        
        # Run uvicorn directly
        cmd = [
            sys.executable, "-m", "uvicorn",
            "src.report_manager.api.server:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("💡 Try: python -m uvicorn src.report_manager.api.server:app --reload")

if __name__ == "__main__":
    run_server()
