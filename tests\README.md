# 🧪 Tests Directory

This directory contains all test files for the Enterprise Multi-Database Query System.

## 📁 Test Structure

```
tests/
├── README.md                           # This file
├── conftest.py                         # Test configuration
├── __init__.py                         # Package initialization
├── unit/                               # Unit tests
│   ├── test_agents.py                  # Agent testing
│   ├── test_api.py                     # API testing
│   └── test_orchestrator.py            # Orchestrator testing
├── integration/                        # Integration tests
│   └── test_full_flow.py               # End-to-end testing
└── functional/                         # Functional tests
    ├── test_multi_database.py          # Multi-database functionality
    ├── test_employee_filtering.py      # Employee filtering tests
    ├── test_name_filtering.py          # Name-based filtering
    ├── test_excel_filtering_direct.py  # Direct Excel filtering
    ├── test_fixed_filtering.py         # Fixed filtering validation
    ├── test_excel_connection.py        # Excel connection tests
    ├── test_db_connection.py           # Database connection tests
    ├── test_clean_response.py          # Response format tests
    ├── test_server.py                  # Server functionality
    └── test_simple_response.py         # Basic response tests
```

## 🚀 Running Tests

### Run All Tests
```bash
# From project root
python -m pytest tests/

# With verbose output
python -m pytest tests/ -v

# With coverage
python -m pytest tests/ --cov=src
```

### Run Specific Test Categories
```bash
# Unit tests only
python -m pytest tests/unit/

# Integration tests only
python -m pytest tests/integration/

# Functional tests only
python -m pytest tests/functional/
```

### Run Individual Tests
```bash
# Test multi-database functionality
python tests/functional/test_multi_database.py

# Test name filtering
python tests/functional/test_name_filtering.py

# Test employee filtering
python tests/functional/test_employee_filtering.py
```

## 🎯 Test Categories

### **Unit Tests** (`tests/unit/`)
- Test individual components in isolation
- Mock external dependencies
- Fast execution
- High code coverage

### **Integration Tests** (`tests/integration/`)
- Test component interactions
- Use real database connections
- Test configuration loading
- Validate data flow

### **Functional Tests** (`tests/functional/`)
- Test complete user scenarios
- End-to-end query processing
- Real database queries
- Response validation

## 📊 Key Test Files

### **Multi-Database Tests**
- `test_multi_database.py` - Tests SQLite + Excel routing
- `test_db_connection.py` - Database connection validation

### **Filtering Tests**
- `test_employee_filtering.py` - Employee query filtering
- `test_name_filtering.py` - Name-based filtering (starts with R)
- `test_excel_filtering_direct.py` - Direct Excel filtering logic
- `test_fixed_filtering.py` - Validation of filtering fixes

### **Connection Tests**
- `test_excel_connection.py` - Excel file connection
- `test_server.py` - Server startup and health

### **Response Tests**
- `test_clean_response.py` - Response format validation
- `test_simple_response.py` - Basic response structure

## 🔧 Test Configuration

### Prerequisites
```bash
# Ensure server is running
python enterprise_server.py

# Or run tests with server auto-start
python -m pytest tests/ --start-server
```

### Environment Variables
```bash
# Set test database paths
export INVOICING_DB_PATH="data/invoicing.db"
export EMPLOYEE_DB_PATH="data/employees.xlsx"
```

### Test Data
- **SQLite Database**: `data/invoicing.db` (32 tax codes)
- **Excel Database**: `data/employees.xlsx` (85 employees)

## ✅ Test Validation

### Expected Results
- **Employee Filtering**: 37 employees with >3 years service
- **Name Filtering**: 13 employees with first name starting with R
- **HR Department**: 14 employees in HR department
- **Tax Codes**: 32 tax codes from einvoicing table

### Success Criteria
- ✅ All queries return filtered results (not all data)
- ✅ SQL generation matches expected patterns
- ✅ Database routing works correctly
- ✅ Response format is consistent
- ✅ No hardcoded values in queries

## 🐛 Troubleshooting Tests

### Common Issues
1. **Server not running**: Start `enterprise_server.py` first
2. **Database files missing**: Check `data/` directory
3. **Port conflicts**: Change server port in tests
4. **Import errors**: Run from project root directory

### Debug Mode
```bash
# Run with debug output
python -m pytest tests/ -v -s --tb=long

# Run single test with debug
python tests/functional/test_name_filtering.py
```

## 📈 Test Coverage

Target coverage areas:
- **SQL Generation**: 95%+ coverage
- **Database Routing**: 100% coverage  
- **Query Execution**: 90%+ coverage
- **Response Formatting**: 100% coverage
- **Error Handling**: 85%+ coverage

## 🎯 Adding New Tests

### For New Database Types
1. Create test file: `test_[database_type]_connection.py`
2. Add to functional tests
3. Update this README

### For New Query Patterns
1. Add test cases to existing files
2. Validate SQL generation
3. Test filtering accuracy

### For New Features
1. Add unit tests first
2. Add integration tests
3. Add functional tests
4. Update documentation

---

**Remember**: Keep tests isolated, fast, and reliable. Each test should validate one specific behavior and be independent of others.
