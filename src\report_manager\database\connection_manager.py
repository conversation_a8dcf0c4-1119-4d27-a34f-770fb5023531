"""
Database Connection Manager

This module manages database connections, connection pooling,
and provides a unified interface for executing queries across
multiple database types.
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime
import json
from loguru import logger

try:
    import sqlalchemy as sa
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
    from sqlalchemy.pool import Queue<PERSON>ool
    from sqlalchemy import text
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    logger.warning("SQLAlchemy not available. Database functionality will be limited.")

try:
    import pandas as pd
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logger.warning("Excel dependencies not available. Excel database functionality will be limited.")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logger.warning("Pandas not available. Some data processing features will be limited.")

from .config_manager import DatabaseConfigManager, DataSource
from .security import DatabaseSecurity, UserSession


@dataclass
class QueryResult:
    """Represents the result of a database query"""
    success: bool
    data: Optional[List[Dict[str, Any]]] = None
    columns: Optional[List[str]] = None
    row_count: int = 0
    execution_time: float = 0.0
    error: Optional[str] = None
    query: Optional[str] = None
    datasource: Optional[str] = None


@dataclass
class ConnectionInfo:
    """Information about a database connection"""
    datasource_name: str
    engine: Any
    session_maker: Any
    created_at: datetime
    last_used: datetime
    connection_count: int = 0

@dataclass
class ExcelConnectionInfo:
    """Information about an Excel file connection"""
    datasource_name: str
    file_path: str
    sheet_name: str
    dataframe: Any  # pandas DataFrame
    created_at: datetime
    last_used: datetime
    connection_count: int = 0


class DatabaseConnectionManager:
    """
    Manages database connections and query execution
    """
    
    def __init__(self, config_manager: DatabaseConfigManager, security_manager: DatabaseSecurity):
        """
        Initialize the connection manager
        
        Args:
            config_manager: Database configuration manager
            security_manager: Database security manager
        """
        self.config_manager = config_manager
        self.security_manager = security_manager
        
        # Connection pools for each data source
        self.connections: Dict[str, ConnectionInfo] = {}
        # Excel connections (separate from SQL connections)
        self.excel_connections: Dict[str, ExcelConnectionInfo] = {}
        
        # Global settings
        self.global_settings = config_manager.config.get('global_settings', {})
        self.query_timeout = self.global_settings.get('query_timeout', 30)
        self.max_connections = self.global_settings.get('max_connections_per_pool', 10)
        
        logger.info("Database connection manager initialized")
    
    async def initialize_connections(self):
        """Initialize connections for all configured data sources"""
        if not SQLALCHEMY_AVAILABLE:
            logger.error("SQLAlchemy not available. Cannot initialize database connections.")
            return
        
        data_sources = self.config_manager.get_all_data_sources()
        
        for ds_name, datasource in data_sources.items():
            try:
                await self._create_connection(ds_name, datasource)
                logger.info(f"Initialized connection for data source: {ds_name}")
            except Exception as e:
                logger.error(f"Failed to initialize connection for {ds_name}: {e}")
    
    async def _create_connection(self, datasource_name: str, datasource: DataSource):
        """
        Create a connection for a specific data source

        Args:
            datasource_name: Name of the data source
            datasource: Data source configuration
        """
        try:
            # Handle Excel files separately
            if datasource.type.lower() == 'excel':
                await self._create_excel_connection(datasource_name, datasource)
                return

            # Build connection URL for SQL databases
            connection_url = self._build_connection_url(datasource)

            # Create async engine with appropriate pool class
            if datasource.type.lower() == 'sqlite':
                # SQLite doesn't support connection pooling in the same way
                engine = create_async_engine(
                    connection_url,
                    echo=self.global_settings.get('enable_query_logging', False)
                )
            else:
                # Use QueuePool for other databases
                engine = create_async_engine(
                    connection_url,
                    poolclass=QueuePool,
                    pool_size=self.max_connections,
                    max_overflow=5,
                    pool_timeout=30,
                    pool_recycle=3600,  # Recycle connections every hour
                    echo=self.global_settings.get('enable_query_logging', False)
                )

            # Create session maker
            session_maker = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            # Store connection info
            self.connections[datasource_name] = ConnectionInfo(
                datasource_name=datasource_name,
                engine=engine,
                session_maker=session_maker,
                created_at=datetime.now(),
                last_used=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error creating connection for {datasource_name}: {e}")
            raise

    async def _create_excel_connection(self, datasource_name: str, datasource: DataSource):
        """
        Create a connection for an Excel file data source

        Args:
            datasource_name: Name of the data source
            datasource: Data source configuration
        """
        if not EXCEL_AVAILABLE:
            raise ImportError("Excel dependencies not available. Install pandas and openpyxl.")

        try:
            connection = datasource.connection
            file_path = connection.get('file_path', '')
            sheet_name = connection.get('sheet_name', 'Sheet1')

            # Resolve environment variables in file path
            import os
            file_path = os.path.expandvars(file_path)

            # Check if file exists
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Excel file not found: {file_path}")

            # Load Excel file into DataFrame
            logger.info(f"Loading Excel file: {file_path}, Sheet: {sheet_name}")
            dataframe = pd.read_excel(file_path, sheet_name=sheet_name)

            # Store Excel connection info
            self.excel_connections[datasource_name] = ExcelConnectionInfo(
                datasource_name=datasource_name,
                file_path=file_path,
                sheet_name=sheet_name,
                dataframe=dataframe,
                created_at=datetime.now(),
                last_used=datetime.now()
            )

            logger.info(f"Excel connection created for {datasource_name}: {len(dataframe)} rows, {len(dataframe.columns)} columns")

        except Exception as e:
            logger.error(f"Error creating Excel connection for {datasource_name}: {e}")
            raise
    
    def _build_connection_url(self, datasource: DataSource) -> str:
        """
        Build database connection URL from data source configuration
        
        Args:
            datasource: Data source configuration
            
        Returns:
            Database connection URL
        """
        db_type = datasource.type.lower()
        connection = datasource.connection
        
        if db_type == 'postgresql':
            # PostgreSQL async URL
            host = connection.get('host', 'localhost')
            port = connection.get('port', 5432)
            database = connection.get('database', '')
            username = connection.get('username', '')
            password = connection.get('password', '')
            
            return f"postgresql+asyncpg://{username}:{password}@{host}:{port}/{database}"
        
        elif db_type == 'mysql':
            # MySQL async URL
            host = connection.get('host', 'localhost')
            port = connection.get('port', 3306)
            database = connection.get('database', '')
            username = connection.get('username', '')
            password = connection.get('password', '')
            
            return f"mysql+aiomysql://{username}:{password}@{host}:{port}/{database}"
        
        elif db_type == 'sqlite':
            # SQLite async URL
            database = connection.get('database', ':memory:')
            return f"sqlite+aiosqlite:///{database}"
        
        elif db_type == 'sqlserver':
            # SQL Server URL (requires additional driver)
            host = connection.get('host', 'localhost')
            port = connection.get('port', 1433)
            database = connection.get('database', '')
            username = connection.get('username', '')
            password = connection.get('password', '')
            
            return f"mssql+aioodbc://{username}:{password}@{host}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server"
        
        elif db_type == 'excel':
            # Excel files don't use connection URLs
            raise ValueError("Excel files should be handled by _create_excel_connection")

        else:
            raise ValueError(f"Unsupported database type: {db_type}")
    
    async def execute_query(self, query: str, datasource_name: str, 
                           user_session: UserSession, parameters: Optional[Dict[str, Any]] = None) -> QueryResult:
        """
        Execute a SQL query with security validation
        
        Args:
            query: SQL query to execute
            datasource_name: Target data source
            user_session: User session for security validation
            parameters: Query parameters
            
        Returns:
            Query result
        """
        start_time = datetime.now()
        
        try:
            # Check if this is an Excel connection
            excel_connection = self.excel_connections.get(datasource_name)
            if excel_connection:
                return await self._execute_excel_query(excel_connection, query, user_session, parameters)

            # Get SQL connection
            connection_info = self.connections.get(datasource_name)
            if not connection_info:
                return QueryResult(
                    success=False,
                    error=f"No connection available for data source: {datasource_name}",
                    query=query,
                    datasource=datasource_name
                )
            
            # Extract tables from query (simplified)
            tables = self._extract_tables_from_query(query)
            
            # Security validation
            is_valid, violations = self.security_manager.validate_query_security(
                query, user_session, datasource_name, tables
            )
            
            if not is_valid:
                violation_messages = [v.description for v in violations]
                return QueryResult(
                    success=False,
                    error=f"Security validation failed: {'; '.join(violation_messages)}",
                    query=query,
                    datasource=datasource_name
                )
            
            # Apply row-level security
            for table in tables:
                query = self.security_manager.add_row_level_security(
                    query, user_session, datasource_name, table
                )
            
            # Sanitize query
            query = self.security_manager.sanitize_query(query)
            
            # Execute query
            result = await self._execute_sql(connection_info, query, parameters)
            
            # Update connection usage
            connection_info.last_used = datetime.now()
            connection_info.connection_count += 1
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            result.query = query
            result.datasource = datasource_name
            
            logger.info(f"Query executed successfully on {datasource_name} in {execution_time:.3f}s")
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Error executing query on {datasource_name}: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                datasource=datasource_name
            )
    
    async def _execute_sql(self, connection_info: ConnectionInfo, query: str, 
                          parameters: Optional[Dict[str, Any]] = None) -> QueryResult:
        """
        Execute SQL query using SQLAlchemy
        
        Args:
            connection_info: Connection information
            query: SQL query
            parameters: Query parameters
            
        Returns:
            Query result
        """
        async with connection_info.session_maker() as session:
            try:
                # Execute query with timeout
                sql_text = text(query)
                
                if parameters:
                    result = await asyncio.wait_for(
                        session.execute(sql_text, parameters),
                        timeout=self.query_timeout
                    )
                else:
                    result = await asyncio.wait_for(
                        session.execute(sql_text),
                        timeout=self.query_timeout
                    )
                
                # Fetch results
                rows = result.fetchall()
                columns = list(result.keys()) if rows else []
                
                # Convert to list of dictionaries
                data = self._convert_rows_to_dict(rows, columns)
                
                # Apply result limit
                max_rows = self.security_manager.security_settings.get('query_result_limit', 10000)
                if len(data) > max_rows:
                    data = data[:max_rows]
                    logger.warning(f"Query result truncated to {max_rows} rows")
                
                return QueryResult(
                    success=True,
                    data=data,
                    columns=columns,
                    row_count=len(data)
                )
                
            except asyncio.TimeoutError:
                raise Exception(f"Query timeout after {self.query_timeout} seconds")
            except Exception as e:
                raise Exception(f"Database error: {str(e)}")

    async def _execute_excel_query(self, excel_connection: ExcelConnectionInfo, query: str,
                                 user_session, parameters: Optional[Dict[str, Any]] = None) -> QueryResult:
        """
        Execute a query on Excel data using pandas

        Args:
            excel_connection: Excel connection info
            query: SQL-like query (simplified for Excel)
            user_session: User session for security
            parameters: Query parameters

        Returns:
            Query result
        """
        try:
            # Update connection usage
            excel_connection.last_used = datetime.now()
            excel_connection.connection_count += 1

            # Implement SQL operations on Excel data using pandas
            df = excel_connection.dataframe

            # Parse and execute SQL query on DataFrame
            query_lower = query.lower().strip()

            if query_lower.startswith('select'):
                result_df = df.copy()

                # Handle WHERE clause
                if 'where' in query_lower:
                    try:
                        # Extract WHERE clause
                        where_part = query.split('WHERE', 1)[1].strip() if 'WHERE' in query else query.split('where', 1)[1].strip()

                        # Parse simple WHERE conditions
                        result_df = self._apply_where_clause(result_df, where_part)

                    except Exception as e:
                        logger.warning(f"Error parsing WHERE clause: {e}, returning all data")
                        result_df = df

                # Handle ORDER BY clause
                if 'order by' in query_lower:
                    try:
                        order_part = query.split('ORDER BY', 1)[1].strip() if 'ORDER BY' in query else query.split('order by', 1)[1].strip()
                        # Remove any trailing semicolon
                        order_part = order_part.rstrip(';')

                        # Simple ORDER BY parsing
                        if 'desc' in order_part.lower():
                            column = order_part.replace('DESC', '').replace('desc', '').strip()
                            result_df = result_df.sort_values(by=column, ascending=False)
                        else:
                            column = order_part.replace('ASC', '').replace('asc', '').strip()
                            result_df = result_df.sort_values(by=column, ascending=True)

                    except Exception as e:
                        logger.warning(f"Error parsing ORDER BY clause: {e}")

                # Handle LIMIT clause
                if 'limit' in query_lower:
                    try:
                        limit_part = query.split('LIMIT', 1)[1].strip() if 'LIMIT' in query else query.split('limit', 1)[1].strip()
                        limit_num = int(limit_part.split()[0])
                        result_df = result_df.head(limit_num)
                    except Exception as e:
                        logger.warning(f"Error parsing LIMIT clause: {e}")

            else:
                result_df = df

            # Apply result limit
            max_rows = self.security_manager.security_settings.get('query_result_limit', 10000)
            if len(result_df) > max_rows:
                result_df = result_df.head(max_rows)
                logger.warning(f"Excel query result truncated to {max_rows} rows")

            # Convert DataFrame to the expected format
            columns = result_df.columns.tolist()
            data = []

            for _, row in result_df.iterrows():
                row_dict = {}
                for col in columns:
                    value = row[col]
                    # Handle pandas/numpy types
                    if pd.isna(value):
                        value = None
                    elif hasattr(value, 'item'):  # numpy types
                        value = value.item()
                    elif hasattr(value, 'isoformat'):  # datetime
                        value = value.isoformat()
                    row_dict[col] = value
                data.append(row_dict)

            return QueryResult(
                success=True,
                data=data,
                columns=columns,
                row_count=len(data)
            )

        except Exception as e:
            logger.error(f"Error executing Excel query: {e}")
            return QueryResult(
                success=False,
                error=f"Excel query error: {str(e)}",
                query=query,
                datasource=excel_connection.datasource_name
            )

    def _apply_where_clause(self, df, where_clause: str):
        """
        Apply WHERE clause filtering to DataFrame

        Args:
            df: pandas DataFrame
            where_clause: WHERE clause string

        Returns:
            Filtered DataFrame
        """
        try:
            import pandas as pd

            # Handle simple conditions
            where_clause = where_clause.strip()

            # Handle AND conditions (split by AND)
            conditions = [cond.strip() for cond in where_clause.split(' AND ')]

            result_df = df.copy()

            for condition in conditions:
                # Parse condition: column operator value
                if '>=' in condition:
                    parts = condition.split('>=')
                    column = parts[0].strip()
                    value = parts[1].strip()

                    # Convert value to appropriate type
                    if value.replace('.', '').isdigit():
                        value = float(value) if '.' in value else int(value)
                    else:
                        value = value.strip("'\"")

                    result_df = result_df[result_df[column] >= value]

                elif '<=' in condition:
                    parts = condition.split('<=')
                    column = parts[0].strip()
                    value = parts[1].strip()

                    if value.replace('.', '').isdigit():
                        value = float(value) if '.' in value else int(value)
                    else:
                        value = value.strip("'\"")

                    result_df = result_df[result_df[column] <= value]

                elif '>' in condition:
                    parts = condition.split('>')
                    column = parts[0].strip()
                    value = parts[1].strip()

                    if value.replace('.', '').isdigit():
                        value = float(value) if '.' in value else int(value)
                    else:
                        value = value.strip("'\"")

                    result_df = result_df[result_df[column] > value]

                elif '<' in condition:
                    parts = condition.split('<')
                    column = parts[0].strip()
                    value = parts[1].strip()

                    if value.replace('.', '').isdigit():
                        value = float(value) if '.' in value else int(value)
                    else:
                        value = value.strip("'\"")

                    result_df = result_df[result_df[column] < value]

                elif 'LIKE' in condition or 'like' in condition:
                    # Handle LIKE conditions for pattern matching
                    parts = condition.split('LIKE') if 'LIKE' in condition else condition.split('like')
                    column = parts[0].strip()
                    pattern = parts[1].strip().strip("'\"")

                    # Convert SQL LIKE pattern to pandas pattern
                    if pattern.endswith('%'):
                        # Starts with pattern (e.g., 'R%')
                        prefix = pattern[:-1]
                        result_df = result_df[result_df[column].astype(str).str.startswith(prefix, na=False)]
                    elif pattern.startswith('%'):
                        # Ends with pattern (e.g., '%son')
                        suffix = pattern[1:]
                        result_df = result_df[result_df[column].astype(str).str.endswith(suffix, na=False)]
                    elif pattern.startswith('%') and pattern.endswith('%'):
                        # Contains pattern (e.g., '%Manager%')
                        substring = pattern[1:-1]
                        result_df = result_df[result_df[column].astype(str).str.contains(substring, na=False, case=False)]
                    else:
                        # Exact match
                        result_df = result_df[result_df[column].astype(str) == pattern]

                elif '=' in condition:
                    parts = condition.split('=')
                    column = parts[0].strip()
                    value = parts[1].strip().strip("'\"")

                    if value.replace('.', '').isdigit():
                        value = float(value) if '.' in value else int(value)

                    result_df = result_df[result_df[column] == value]

            logger.info(f"Applied WHERE clause: {where_clause}, filtered from {len(df)} to {len(result_df)} rows")
            return result_df

        except Exception as e:
            logger.error(f"Error applying WHERE clause '{where_clause}': {e}")
            return df  # Return original DataFrame if filtering fails

    def _convert_rows_to_dict(self, rows, columns) -> List[Dict[str, Any]]:
        """Convert database rows to list of dictionaries"""
        data = []
        for row in rows:
            row_dict = {}
            for i, column in enumerate(columns):
                value = row[i]
                # Convert non-serializable types
                if hasattr(value, 'isoformat'):  # datetime objects
                    value = value.isoformat()
                elif isinstance(value, (bytes, bytearray)):
                    value = value.decode('utf-8', errors='ignore')
                row_dict[column] = value
            data.append(row_dict)
        return data
    
    def _extract_tables_from_query(self, query: str) -> List[str]:
        """
        Extract table names from SQL query (simplified implementation)
        
        Args:
            query: SQL query
            
        Returns:
            List of table names
        """
        import re
        
        # Simple regex to find table names after FROM and JOIN
        tables = []
        
        # Find FROM clauses
        from_matches = re.findall(r'\bFROM\s+(\w+)', query, re.IGNORECASE)
        tables.extend(from_matches)
        
        # Find JOIN clauses
        join_matches = re.findall(r'\bJOIN\s+(\w+)', query, re.IGNORECASE)
        tables.extend(join_matches)
        
        # Remove duplicates
        return list(set(tables))
    
    async def test_connection(self, datasource_name: str) -> bool:
        """
        Test a database connection
        
        Args:
            datasource_name: Name of the data source
            
        Returns:
            True if connection is successful
        """
        try:
            connection_info = self.connections.get(datasource_name)
            if not connection_info:
                return False
            
            # Simple test query
            test_query = "SELECT 1 as test"
            
            async with connection_info.session_maker() as session:
                result = await session.execute(text(test_query))
                result.fetchone()
            
            logger.info(f"Connection test successful for {datasource_name}")
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed for {datasource_name}: {e}")
            return False
    
    async def get_table_schema(self, datasource_name: str, table_name: str, 
                              user_session: UserSession) -> Optional[Dict[str, Any]]:
        """
        Get schema information for a table
        
        Args:
            datasource_name: Data source name
            table_name: Table name
            user_session: User session
            
        Returns:
            Table schema information
        """
        try:
            # Check permissions
            if not self.config_manager.validate_user_permissions(
                user_session.role, datasource_name, table_name
            ):
                logger.warning(f"User {user_session.user_id} denied access to {datasource_name}.{table_name}")
                return None
            
            # Get schema from configuration
            datasource = self.config_manager.get_data_source(datasource_name)
            if not datasource or table_name not in datasource.tables:
                return None
            
            table_info = datasource.tables[table_name]
            
            return {
                "table_name": table_name,
                "description": table_info.description,
                "columns": {
                    col_name: {
                        "type": col_info.type,
                        "description": col_info.description
                    }
                    for col_name, col_info in table_info.columns.items()
                },
                "keywords": table_info.keywords,
                "common_queries": table_info.common_queries
            }
            
        except Exception as e:
            logger.error(f"Error getting table schema: {e}")
            return None
    
    async def close_connections(self):
        """Close all database connections"""
        for datasource_name, connection_info in self.connections.items():
            try:
                await connection_info.engine.dispose()
                logger.info(f"Closed connection for {datasource_name}")
            except Exception as e:
                logger.error(f"Error closing connection for {datasource_name}: {e}")
        
        self.connections.clear()
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get status of all connections

        Returns:
            Connection status information
        """
        status = {
            "total_connections": len(self.connections) + len(self.excel_connections),
            "sql_connections": len(self.connections),
            "excel_connections": len(self.excel_connections),
            "connections": {}
        }

        # SQL connections
        for ds_name, conn_info in self.connections.items():
            status["connections"][ds_name] = {
                "type": "sql",
                "created_at": conn_info.created_at.isoformat(),
                "last_used": conn_info.last_used.isoformat(),
                "connection_count": conn_info.connection_count,
                "pool_size": getattr(conn_info.engine.pool, 'size', 'unknown'),
                "checked_out": getattr(conn_info.engine.pool, 'checkedout', 'unknown')
            }

        # Excel connections
        for ds_name, excel_info in self.excel_connections.items():
            status["connections"][ds_name] = {
                "type": "excel",
                "file_path": excel_info.file_path,
                "sheet_name": excel_info.sheet_name,
                "rows": len(excel_info.dataframe),
                "columns": len(excel_info.dataframe.columns),
                "created_at": excel_info.created_at.isoformat(),
                "last_used": excel_info.last_used.isoformat(),
                "connection_count": excel_info.connection_count
            }

        return status
