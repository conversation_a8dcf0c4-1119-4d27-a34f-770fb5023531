#!/usr/bin/env python3
"""
Simple test server to debug the issue
"""

import os
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🔍 Environment Variables Check:")
print(f"   INVOICING_DB_TYPE: {os.getenv('INVOICING_DB_TYPE')}")
print(f"   INVOICING_DB_PATH: {os.getenv('INVOICING_DB_PATH')}")
print(f"   Database exists: {os.path.exists(os.getenv('INVOICING_DB_PATH', 'data/invoicing.db'))}")

if __name__ == "__main__":
    print("🚀 Starting test server on port 8002...")
    uvicorn.run(
        "src.report_manager.api.server:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info"
    )
